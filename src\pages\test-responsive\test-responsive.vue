<template>
  <view class="test-container" :class="responsiveClasses">
    <!-- 设备信息显示 -->
    <view class="device-info">
      <text class="info-title">设备信息测试</text>
      <text class="info-item">断点: {{ currentBreakpoint }}</text>
      <text class="info-item">宽度: {{ deviceInfo?.windowWidth }}px</text>
      <text class="info-item">平台: {{ deviceInfo?.platform }}</text>
      <text class="info-item">小屏: {{ isSmallScreen ? '是' : '否' }}</text>
      <text class="info-item">大屏: {{ isLargeScreen ? '是' : '否' }}</text>
    </view>

    <!-- 2x2网格测试 -->
    <view class="test-section">
      <text class="section-title">2x2网格测试</text>
      <view class="test-grid-2x2">
        <view class="grid-item item-1">
          <text class="item-title">卡片1</text>
          <text class="item-content">内容1</text>
        </view>
        <view class="grid-item item-2">
          <text class="item-title">卡片2</text>
          <text class="item-content">内容2</text>
        </view>
        <view class="grid-item item-3">
          <text class="item-title">卡片3</text>
          <text class="item-content">内容3</text>
        </view>
        <view class="grid-item item-4">
          <text class="item-title">卡片4</text>
          <text class="item-content">内容4</text>
        </view>
      </view>
    </view>

    <!-- 字体大小测试 -->
    <view class="test-section">
      <text class="section-title">字体大小测试</text>
      <view class="font-test">
        <text class="test-font-xs">XS字体</text>
        <text class="test-font-sm">SM字体</text>
        <text class="test-font-base">基础字体</text>
        <text class="test-font-lg">LG字体</text>
        <text class="test-font-xl">XL字体</text>
      </view>
    </view>

    <!-- 间距测试 */
    <view class="test-section">
      <text class="section-title">间距测试</text>
      <view class="spacing-test">
        <view class="spacing-item xs">XS间距</view>
        <view class="spacing-item sm">SM间距</view>
        <view class="spacing-item base">基础间距</view>
        <view class="spacing-item lg">LG间距</view>
      </view>
    </view>
  </view>
</template>

<script>
import responsiveMixin from '../../mixins/responsiveMixin.js'

export default {
  name: 'TestResponsive',
  mixins: [responsiveMixin],
  
  onLoad() {
    uni.setNavigationBarTitle({
      title: '响应式测试'
    })
  }
}
</script>

<style scoped>
.test-container {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: var(--space-base);
}

.device-info {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin: 64px 0 var(--space-xl);
}

.info-title {
  font-size: var(--font-lg);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-base);
  display: block;
}

.info-item {
  font-size: var(--font-base);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
  display: block;
}

.test-section {
  margin-bottom: var(--space-2xl);
}

.section-title {
  font-size: var(--font-lg);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  display: block;
}

/* 2x2网格测试 */
.test-grid-2x2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-base);
}

.grid-item {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-base);
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.item-1 { background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white; }
.item-2 { background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white; }
.item-3 { background: linear-gradient(135deg, #45b7d1, #96c93d); color: white; }
.item-4 { background: linear-gradient(135deg, #f093fb, #f5576c); color: white; }

.item-title {
  font-size: var(--font-base);
  font-weight: 700;
  margin-bottom: var(--space-xs);
}

.item-content {
  font-size: var(--font-sm);
  opacity: 0.9;
}

/* 字体测试 */
.font-test {
  display: flex;
  flex-direction: column;
  gap: var(--space-base);
}

.test-font-xs { font-size: var(--font-xs); }
.test-font-sm { font-size: var(--font-sm); }
.test-font-base { font-size: var(--font-base); }
.test-font-lg { font-size: var(--font-lg); }
.test-font-xl { font-size: var(--font-xl); }

/* 间距测试 */
.spacing-test {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.spacing-item {
  background: var(--bg-card);
  border-radius: var(--radius-base);
  border: 2rpx solid var(--primary-color);
  color: var(--text-primary);
  font-size: var(--font-sm);
  font-weight: 600;
}

.spacing-item.xs { padding: var(--space-xs); }
.spacing-item.sm { padding: var(--space-sm); }
.spacing-item.base { padding: var(--space-base); }
.spacing-item.lg { padding: var(--space-lg); }

/* 响应式调整 */
.breakpoint-xs .test-grid-2x2 {
  gap: var(--space-sm);
}

.breakpoint-xs .grid-item {
  padding: var(--space-base);
  min-height: 100rpx;
}

.breakpoint-xs .item-title {
  font-size: var(--font-sm);
}

.breakpoint-xs .item-content {
  font-size: var(--font-xs);
}

.breakpoint-sm .test-grid-2x2 {
  gap: var(--space-base);
}

.breakpoint-xl .test-grid-2x2,
.breakpoint-xxl .test-grid-2x2 {
  gap: var(--space-lg);
}

.breakpoint-xl .grid-item,
.breakpoint-xxl .grid-item {
  padding: var(--space-xl);
  min-height: 150rpx;
}
</style>
