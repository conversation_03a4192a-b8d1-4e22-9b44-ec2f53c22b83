/**
 * 屏幕适配工具函数
 * 基于设计稿宽度430px（iPhone 14 Pro Max），转换为750rpx的标准
 */

// 设计稿宽度（iPhone 14 Pro Max的设计基准）
const DESIGN_WIDTH = 430;
// uni-app标准宽度
const DEFAULT_WIDTH = 750;

/**
 * px转rpx的核心函数
 * @param {number} px - 设计稿中的px值
 * @returns {number} - 转换后的rpx值
 */
export function pxToRpx(px) {
  return (px / DESIGN_WIDTH) * DEFAULT_WIDTH;
}

/**
 * 批量转换px到rpx
 * @param {object} styles - 包含px值的样式对象
 * @returns {object} - 转换后的rpx样式对象
 */
export function convertStylesToRpx(styles) {
  const convertedStyles = {};
  
  for (const [key, value] of Object.entries(styles)) {
    if (typeof value === 'number') {
      convertedStyles[key] = pxToRpx(value);
    } else if (typeof value === 'string' && value.includes('px')) {
      // 处理字符串中的px值
      convertedStyles[key] = value.replace(/(\d+(?:\.\d+)?)px/g, (match, num) => {
        return `${pxToRpx(parseFloat(num))}rpx`;
      });
    } else {
      convertedStyles[key] = value;
    }
  }
  
  return convertedStyles;
}

/**
 * 获取当前设备信息
 * @returns {Promise<object>} - 设备信息
 */
export function getDeviceInfo() {
  return new Promise((resolve) => {
    uni.getSystemInfo({
      success: (res) => {
        const deviceInfo = {
          screenWidth: res.screenWidth,
          screenHeight: res.screenHeight,
          windowWidth: res.windowWidth,
          windowHeight: res.windowHeight,
          pixelRatio: res.pixelRatio,
          platform: res.platform,
          model: res.model,
          // 计算缩放比例
          scale: res.windowWidth / DEFAULT_WIDTH,
          // 判断设备类型
          isIPhone: res.platform === 'ios',
          isAndroid: res.platform === 'android',
          // 判断屏幕尺寸类型
          isSmallScreen: res.windowWidth < 375,
          isMediumScreen: res.windowWidth >= 375 && res.windowWidth < 414,
          isLargeScreen: res.windowWidth >= 414
        };
        resolve(deviceInfo);
      },
      fail: () => {
        // 默认值
        resolve({
          screenWidth: 375,
          screenHeight: 667,
          windowWidth: 375,
          windowHeight: 667,
          pixelRatio: 2,
          platform: 'unknown',
          model: 'unknown',
          scale: 375 / DEFAULT_WIDTH,
          isIPhone: false,
          isAndroid: false,
          isSmallScreen: false,
          isMediumScreen: true,
          isLargeScreen: false
        });
      }
    });
  });
}

/**
 * 根据设备类型调整尺寸
 * @param {number} baseSize - 基础尺寸（rpx）
 * @param {object} deviceInfo - 设备信息
 * @returns {number} - 调整后的尺寸
 */
export function adaptSize(baseSize, deviceInfo) {
  if (!deviceInfo) return baseSize;
  
  // 根据屏幕尺寸调整
  if (deviceInfo.isSmallScreen) {
    return baseSize * 0.9; // 小屏幕缩小10%
  } else if (deviceInfo.isLargeScreen) {
    return baseSize * 1.1; // 大屏幕放大10%
  }
  
  return baseSize;
}

/**
 * 响应式字体大小
 * @param {number} baseFontSize - 基础字体大小（rpx）
 * @param {object} deviceInfo - 设备信息
 * @returns {number} - 调整后的字体大小
 */
export function adaptFontSize(baseFontSize, deviceInfo) {
  if (!deviceInfo) return baseFontSize;
  
  // 根据屏幕尺寸调整字体
  if (deviceInfo.isSmallScreen) {
    return Math.max(baseFontSize * 0.85, 20); // 最小20rpx
  } else if (deviceInfo.isLargeScreen) {
    return baseFontSize * 1.15;
  }
  
  return baseFontSize;
}

/**
 * 响应式间距
 * @param {number} baseSpacing - 基础间距（rpx）
 * @param {object} deviceInfo - 设备信息
 * @returns {number} - 调整后的间距
 */
export function adaptSpacing(baseSpacing, deviceInfo) {
  if (!deviceInfo) return baseSpacing;
  
  // 根据屏幕尺寸调整间距
  if (deviceInfo.isSmallScreen) {
    return Math.max(baseSpacing * 0.8, 8); // 最小8rpx
  } else if (deviceInfo.isLargeScreen) {
    return baseSpacing * 1.2;
  }
  
  return baseSpacing;
}

/**
 * 生成响应式样式类
 * @param {object} baseStyles - 基础样式
 * @param {object} deviceInfo - 设备信息
 * @returns {object} - 响应式样式
 */
export function generateResponsiveStyles(baseStyles, deviceInfo) {
  if (!deviceInfo) return baseStyles;
  
  const responsiveStyles = { ...baseStyles };
  
  // 调整字体相关属性
  if (responsiveStyles.fontSize) {
    responsiveStyles.fontSize = adaptFontSize(responsiveStyles.fontSize, deviceInfo);
  }
  
  // 调整间距相关属性
  const spacingProps = ['padding', 'margin', 'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft',
                       'marginTop', 'marginRight', 'marginBottom', 'marginLeft', 'gap'];
  
  spacingProps.forEach(prop => {
    if (responsiveStyles[prop]) {
      responsiveStyles[prop] = adaptSpacing(responsiveStyles[prop], deviceInfo);
    }
  });
  
  // 调整尺寸相关属性
  const sizeProps = ['width', 'height', 'minWidth', 'minHeight', 'maxWidth', 'maxHeight'];
  
  sizeProps.forEach(prop => {
    if (responsiveStyles[prop]) {
      responsiveStyles[prop] = adaptSize(responsiveStyles[prop], deviceInfo);
    }
  });
  
  return responsiveStyles;
}

/**
 * 常用尺寸转换预设
 */
export const CommonSizes = {
  // 字体大小
  fontXS: pxToRpx(10),      // 19.23rpx
  fontSM: pxToRpx(12),      // 23.08rpx  
  fontBase: pxToRpx(14),    // 26.92rpx
  fontLG: pxToRpx(16),      // 30.77rpx
  fontXL: pxToRpx(18),      // 34.62rpx
  font2XL: pxToRpx(22),     // 42.31rpx
  font3XL: pxToRpx(26),     // 50rpx
  font4XL: pxToRpx(32),     // 61.54rpx
  
  // 间距
  spaceXS: pxToRpx(4),      // 7.69rpx
  spaceSM: pxToRpx(8),      // 15.38rpx
  spaceBase: pxToRpx(12),   // 23.08rpx
  spaceLG: pxToRpx(16),     // 30.77rpx
  spaceXL: pxToRpx(20),     // 38.46rpx
  space2XL: pxToRpx(24),    // 46.15rpx
  space3XL: pxToRpx(32),    // 61.54rpx
  space4XL: pxToRpx(40),    // 76.92rpx
  
  // 圆角
  radiusXS: pxToRpx(2),     // 3.85rpx
  radiusSM: pxToRpx(4),     // 7.69rpx
  radiusBase: pxToRpx(6),   // 11.54rpx
  radiusLG: pxToRpx(8),     // 15.38rpx
  radiusXL: pxToRpx(12),    // 23.08rpx
  radius2XL: pxToRpx(16),   // 30.77rpx
  radius3XL: pxToRpx(20),   // 38.46rpx
  radiusFull: 9999,
  
  // 常用组件尺寸
  buttonHeight: pxToRpx(44),    // 84.62rpx
  inputHeight: pxToRpx(40),     // 76.92rpx
  cardPadding: pxToRpx(16),     // 30.77rpx
  sectionSpacing: pxToRpx(24),  // 46.15rpx
  
  // 图标尺寸
  iconXS: pxToRpx(12),      // 23.08rpx
  iconSM: pxToRpx(16),      // 30.77rpx
  iconBase: pxToRpx(20),    // 38.46rpx
  iconLG: pxToRpx(24),      // 46.15rpx
  iconXL: pxToRpx(32),      // 61.54rpx
  icon2XL: pxToRpx(40),     // 76.92rpx
};

/**
 * 创建响应式样式混入
 * @param {object} deviceInfo - 设备信息
 * @returns {object} - 样式混入对象
 */
export function createResponsiveMixin(deviceInfo) {
  return {
    data() {
      return {
        deviceInfo,
        CommonSizes
      };
    },
    methods: {
      pxToRpx,
      adaptSize: (size) => adaptSize(size, deviceInfo),
      adaptFontSize: (size) => adaptFontSize(size, deviceInfo),
      adaptSpacing: (size) => adaptSpacing(size, deviceInfo),
      generateResponsiveStyles: (styles) => generateResponsiveStyles(styles, deviceInfo)
    }
  };
}

// 导出默认设备信息（用于SSR或初始化）
export const defaultDeviceInfo = {
  screenWidth: 390,
  screenHeight: 844,
  windowWidth: 390,
  windowHeight: 844,
  pixelRatio: 3,
  platform: 'ios',
  model: 'iPhone XS MAX',
  scale: 390 / DEFAULT_WIDTH,
  isIPhone: true,
  isAndroid: false,
  isSmallScreen: false,
  isMediumScreen: false,
  isLargeScreen: true
};
