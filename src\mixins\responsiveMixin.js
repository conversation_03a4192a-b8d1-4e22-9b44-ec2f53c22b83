/**
 * 响应式组件混入
 * 为所有组件提供响应式适配能力
 */

import responsiveManager, { pxToRpx, CommonSizes } from '../utils/responsiveManager.js';
import dynamicScaleManager from '../utils/dynamicScaleManager.js';

export default {
  data() {
    return {
      // 设备信息
      deviceInfo: null,
      // 当前断点
      currentBreakpoint: 'md',
      // 常用尺寸
      CommonSizes,
      // 响应式状态
      isSmallScreen: false,
      isLargeScreen: false,
      // 动态缩放
      currentScale: 1,
    };
  },

  async created() {
    // 等待响应式管理器初始化
    if (!responsiveManager.isInitialized) {
      await responsiveManager.init();
    }

    // 等待动态缩放管理器初始化
    if (!dynamicScaleManager.isInitialized) {
      await dynamicScaleManager.init();
    }

    // 获取设备信息
    this.deviceInfo = responsiveManager.getDeviceInfo();
    this.currentBreakpoint = responsiveManager.getCurrentBreakpoint();
    this.isSmallScreen = responsiveManager.isSmallScreen();
    this.isLargeScreen = responsiveManager.isLargeScreen();
    this.currentScale = dynamicScaleManager.getCurrentScale();

    // 监听响应式变化
    responsiveManager.addListener(this.onResponsiveChange);
    dynamicScaleManager.addListener(this.onScaleChange);
  },

  beforeUnmount() {
    // 移除监听器
    responsiveManager.removeListener(this.onResponsiveChange);
    dynamicScaleManager.removeListener(this.onScaleChange);
  },

  methods: {
    /**
     * px转rpx
     */
    pxToRpx,

    /**
     * 响应式变化回调
     */
    onResponsiveChange(deviceInfo, breakpoint) {
      this.deviceInfo = deviceInfo;
      this.currentBreakpoint = breakpoint;
      this.isSmallScreen = responsiveManager.isSmallScreen();
      this.isLargeScreen = responsiveManager.isLargeScreen();

      // 触发组件更新
      this.$forceUpdate();
    },

    /**
     * 缩放变化回调
     */
    onScaleChange(scale) {
      this.currentScale = scale;

      // 触发组件更新
      this.$forceUpdate();
    },

    /**
     * 获取响应式样式
     */
    getResponsiveStyles(baseStyles) {
      return responsiveManager.getResponsiveStyles(baseStyles);
    },

    /**
     * 获取响应式字体大小
     */
    getResponsiveFontSize(baseFontSize) {
      return responsiveManager.getResponsiveFontSize(baseFontSize);
    },

    /**
     * 获取响应式间距
     */
    getResponsiveSpacing(baseSpacing) {
      return responsiveManager.getResponsiveSpacing(baseSpacing);
    },

    /**
     * 获取网格列数
     */
    getGridColumns(defaultColumns = 2) {
      return responsiveManager.getGridColumns(defaultColumns);
    },

    /**
     * 创建响应式CSS类名
     */
    createResponsiveClass(baseClass) {
      return responsiveManager.createResponsiveClass(baseClass);
    },

    /**
     * 获取动态缩放样式
     */
    getScaledStyles(baseStyles) {
      return dynamicScaleManager.getScaledStyles(baseStyles);
    },

    /**
     * 根据动态缩放调整数值
     */
    scaleValue(value, type = 'size') {
      return dynamicScaleManager.scaleValue(value, type);
    },

    /**
     * 创建动态缩放CSS类名
     */
    createScaledClass(baseClass) {
      return dynamicScaleManager.createScaledClass(baseClass);
    },

    /**
     * 根据屏幕大小调整数值
     */
    adaptValue(value, type = 'size') {
      if (!this.deviceInfo) return value;
      
      switch (type) {
        case 'font':
          return this.getResponsiveFontSize(value);
        case 'spacing':
          return this.getResponsiveSpacing(value);
        case 'size':
        default:
          const breakpoint = this.currentBreakpoint;
          const scales = {
            xs: 0.85,
            sm: 0.9,
            md: 1,
            lg: 1.1,
            xl: 1.15,
            xxl: 1.2
          };
          const scale = scales[breakpoint] || 1;
          return Math.round(value * scale);
      }
    },

    /**
     * 获取响应式卡片样式
     */
    getCardStyles() {
      const baseStyles = {
        padding: this.pxToRpx(16),
        borderRadius: this.pxToRpx(12),
        marginBottom: this.pxToRpx(16)
      };
      
      return this.getResponsiveStyles(baseStyles);
    },

    /**
     * 获取响应式按钮样式
     */
    getButtonStyles() {
      const baseStyles = {
        height: this.pxToRpx(44),
        padding: `0 ${this.pxToRpx(20)}`,
        fontSize: this.pxToRpx(16),
        borderRadius: this.pxToRpx(8)
      };
      
      return this.getResponsiveStyles(baseStyles);
    },

    /**
     * 获取响应式文本样式
     */
    getTextStyles(size = 'base') {
      const sizeMap = {
        xs: this.CommonSizes.fontXS,
        sm: this.CommonSizes.fontSM,
        base: this.CommonSizes.fontBase,
        lg: this.CommonSizes.fontLG,
        xl: this.CommonSizes.fontXL,
        '2xl': this.CommonSizes.font2XL,
        '3xl': this.CommonSizes.font3XL,
        '4xl': this.CommonSizes.font4XL
      };
      
      const fontSize = sizeMap[size] || this.CommonSizes.fontBase;
      
      return {
        fontSize: this.getResponsiveFontSize(fontSize)
      };
    },

    /**
     * 获取响应式间距样式
     */
    getSpacingStyles(spacing = 'base') {
      const spacingMap = {
        xs: this.CommonSizes.spaceXS,
        sm: this.CommonSizes.spaceSM,
        base: this.CommonSizes.spaceBase,
        lg: this.CommonSizes.spaceLG,
        xl: this.CommonSizes.spaceXL,
        '2xl': this.CommonSizes.space2XL,
        '3xl': this.CommonSizes.space3XL,
        '4xl': this.CommonSizes.space4XL
      };
      
      const spaceValue = spacingMap[spacing] || this.CommonSizes.spaceBase;
      
      return {
        padding: this.getResponsiveSpacing(spaceValue)
      };
    },

    /**
     * 获取响应式图标尺寸
     */
    getIconSize(size = 'base') {
      const sizeMap = {
        xs: this.CommonSizes.iconXS,
        sm: this.CommonSizes.iconSM,
        base: this.CommonSizes.iconBase,
        lg: this.CommonSizes.iconLG,
        xl: this.CommonSizes.iconXL,
        '2xl': this.CommonSizes.icon2XL
      };
      
      const iconSize = sizeMap[size] || this.CommonSizes.iconBase;
      
      return this.adaptValue(iconSize, 'size');
    },

    /**
     * 判断是否为指定断点
     */
    isBreakpoint(breakpoint) {
      return this.currentBreakpoint === breakpoint;
    },

    /**
     * 判断是否大于等于指定断点
     */
    isBreakpointUp(breakpoint) {
      const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
      const currentIndex = breakpoints.indexOf(this.currentBreakpoint);
      const targetIndex = breakpoints.indexOf(breakpoint);
      return currentIndex >= targetIndex;
    },

    /**
     * 判断是否小于等于指定断点
     */
    isBreakpointDown(breakpoint) {
      const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
      const currentIndex = breakpoints.indexOf(this.currentBreakpoint);
      const targetIndex = breakpoints.indexOf(breakpoint);
      return currentIndex <= targetIndex;
    },

    /**
     * 获取响应式网格样式
     */
    getGridStyles(columns = 2) {
      const responsiveColumns = this.getGridColumns(columns);
      const gap = this.getResponsiveSpacing(this.CommonSizes.spaceBase);
      
      return {
        display: 'grid',
        gridTemplateColumns: `repeat(${responsiveColumns}, 1fr)`,
        gap: `${gap}rpx`
      };
    },

    /**
     * 获取响应式容器样式
     */
    getContainerStyles() {
      const padding = this.getResponsiveSpacing(this.CommonSizes.spaceBase);
      
      return {
        padding: `${padding}rpx`,
        maxWidth: this.isLargeScreen ? '1200rpx' : '100%',
        margin: this.isLargeScreen ? '0 auto' : '0'
      };
    }
  },

  computed: {
    /**
     * 响应式类名
     */
    responsiveClasses() {
      return [
        `breakpoint-${this.currentBreakpoint}`,
        this.isSmallScreen ? 'small-screen' : '',
        this.isLargeScreen ? 'large-screen' : '',
        this.deviceInfo?.platform ? `platform-${this.deviceInfo.platform}` : ''
      ].filter(Boolean).join(' ');
    },

    /**
     * 设备类型类名
     */
    deviceClasses() {
      if (!this.deviceInfo) return '';
      
      return [
        this.deviceInfo.isIPhone ? 'device-iphone' : '',
        this.deviceInfo.isAndroid ? 'device-android' : '',
        `pixel-ratio-${Math.floor(this.deviceInfo.pixelRatio || 2)}`
      ].filter(Boolean).join(' ');
    }
  }
};
