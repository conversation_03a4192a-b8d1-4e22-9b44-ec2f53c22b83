/* 响应式全局样式 - 基于390px设计稿适配 */

/* 字体设置 */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

/* 响应式全局变量 - 继承uni.scss的适配系统 */
:root {
  /* 颜色系统 - 保持与uni.scss一致 */
  --primary-color: #00b4d8;
  --secondary-color: #0096c7;
  --accent-color: #f9ad3d;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --info-color: #2196f3;
  
  /* 背景色 */
  --bg-primary: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
  --bg-secondary: #f0f0f0;
  --bg-card: #ffffff;
  --bg-overlay: rgba(255, 255, 255, 0.95);
  
  /* 文字颜色 */
  --text-primary: #222;
  --text-secondary: #666;
  --text-muted: #999;
  --text-light: #aaa;
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.08);
  --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.12);
  
  /* 圆角 - 使用uni.scss的响应式变量 */
  --radius-sm: var(--radius-sm);
  --radius-md: var(--radius-lg);
  --radius-lg: var(--radius-xl);
  --radius-xl: var(--radius-2xl);
  --radius-full: var(--radius-full);

  /* 间距 - 使用uni.scss的响应式变量 */
  --space-xs: var(--space-xs);
  --space-sm: var(--space-sm);
  --space-md: var(--space-base);
  --space-lg: var(--space-lg);
  --space-xl: var(--space-xl);
  --space-2xl: var(--space-2xl);
  
  /* 字体大小 - 使用uni.scss的响应式变量 */
  --text-xs: var(--font-xs);
  --text-sm: var(--font-sm);
  --text-base: var(--font-base);
  --text-lg: var(--font-lg);
  --text-xl: var(--font-xl);
  --text-2xl: var(--font-2xl);
  --text-3xl: var(--font-3xl);
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

page {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用动画类 */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideUp {
  animation: slideUp 0.6s ease-out forwards;
}

.animate-slideDown {
  animation: slideDown 0.6s ease-out forwards;
}

.animate-slideLeft {
  animation: slideLeft 0.6s ease-out forwards;
}

.animate-slideRight {
  animation: slideRight 0.6s ease-out forwards;
}

.animate-bounce {
  animation: bounce 1s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

/* 通用工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.rounded {
  border-radius: var(--radius-md);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-full {
  border-radius: var(--radius-full);
}

.shadow {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.transition {
  transition: all var(--transition-base);
}

.transition-fast {
  transition: all var(--transition-fast);
}

.transition-slow {
  transition: all var(--transition-slow);
}

/* 响应式设计 - 使用uni.scss的断点系统 */
/* 媒体查询已在uni.scss中统一管理，这里只做补充 */

/* 通用响应式系统 - 基于iPhone 14 Pro Max (430px) 为标准 */
/* 使用连续缩放，适配所有屏幕宽度，保持布局结构不变 */

:root {
  /* 精确的视口宽度比例缩放系统 */
  /* 基准: iPhone 14 Pro Max = 430px = 1.0倍 */
  --scale-factor: calc(100vw / 430px);
  --scale-factor-clamped: clamp(0.65, var(--scale-factor), 1.5);

  /* 保持原有设计的精确比例缩放 */
  /* 字体大小 - 保持原有相对比例 */
  --font-xs-scaled: calc(24rpx * var(--scale-factor-clamped));
  --font-sm-scaled: calc(28rpx * var(--scale-factor-clamped));
  --font-base-scaled: calc(32rpx * var(--scale-factor-clamped));
  --font-lg-scaled: calc(36rpx * var(--scale-factor-clamped));
  --font-xl-scaled: calc(40rpx * var(--scale-factor-clamped));
  --font-2xl-scaled: calc(48rpx * var(--scale-factor-clamped));
  --font-3xl-scaled: calc(56rpx * var(--scale-factor-clamped));

  /* 间距 - 保持原有相对比例 */
  --space-xs-scaled: calc(8rpx * var(--scale-factor-clamped));
  --space-sm-scaled: calc(12rpx * var(--scale-factor-clamped));
  --space-base-scaled: calc(16rpx * var(--scale-factor-clamped));
  --space-lg-scaled: calc(24rpx * var(--scale-factor-clamped));
  --space-xl-scaled: calc(32rpx * var(--scale-factor-clamped));
  --space-2xl-scaled: calc(48rpx * var(--scale-factor-clamped));
  --space-3xl-scaled: calc(64rpx * var(--scale-factor-clamped));

  /* 圆角 - 保持原有相对比例 */
  --radius-xs-scaled: calc(4rpx * var(--scale-factor-clamped));
  --radius-sm-scaled: calc(8rpx * var(--scale-factor-clamped));
  --radius-base-scaled: calc(12rpx * var(--scale-factor-clamped));
  --radius-lg-scaled: calc(16rpx * var(--scale-factor-clamped));
  --radius-xl-scaled: calc(24rpx * var(--scale-factor-clamped));

  /* 边框宽度 - 保持原有相对比例 */
  --border-width-scaled: calc(1rpx * var(--scale-factor-clamped));
  --border-width-2-scaled: calc(2rpx * var(--scale-factor-clamped));
}

/* 保持原有布局结构的比例缩放系统 */
/* 不强制改变布局，只调整尺寸比例 */

/* 应用比例缩放到页面根元素 */
page {
  font-size: calc(32rpx * var(--scale-factor-clamped));
}

/* 通用比例缩放应用 */
.responsive-grid,
.info-cards-section,
.stats-cards,
.time-weather-section,
.data-grid,
.quick-overview,
.secondary-data-grid,
.bottom-data-grid,
.info-grid-redesigned,
.actions-grid {
  /* 保持原有的grid-template-columns设置，只调整gap */
  gap: var(--space-base-scaled);
}

/* 确保2列网格保持2列（不强制，只是确保） */
.grid-2-cols,
.info-cards-section {
  grid-template-columns: repeat(2, 1fr);
}

/* 通用容器样式 - 使用精确比例缩放 */
.responsive-container {
  width: 100%;
  padding: var(--space-base-scaled);
  margin: 0 auto;
  max-width: calc(800px * var(--scale-factor-clamped));
}

/* 比例缩放的文字大小类 */
.text-xs-scaled { font-size: var(--font-xs-scaled); }
.text-sm-scaled { font-size: var(--font-sm-scaled); }
.text-base-scaled { font-size: var(--font-base-scaled); }
.text-lg-scaled { font-size: var(--font-lg-scaled); }
.text-xl-scaled { font-size: var(--font-xl-scaled); }
.text-2xl-scaled { font-size: var(--font-2xl-scaled); }
.text-3xl-scaled { font-size: var(--font-3xl-scaled); }

/* 比例缩放的间距类 */
.p-xs-scaled { padding: var(--space-xs-scaled); }
.p-sm-scaled { padding: var(--space-sm-scaled); }
.p-base-scaled { padding: var(--space-base-scaled); }
.p-lg-scaled { padding: var(--space-lg-scaled); }
.p-xl-scaled { padding: var(--space-xl-scaled); }

.m-xs-scaled { margin: var(--space-xs-scaled); }
.m-sm-scaled { margin: var(--space-sm-scaled); }
.m-base-scaled { margin: var(--space-base-scaled); }
.m-lg-scaled { margin: var(--space-lg-scaled); }
.m-xl-scaled { margin: var(--space-xl-scaled); }

/* 比例缩放的圆角类 */
.rounded-xs-scaled { border-radius: var(--radius-xs-scaled); }
.rounded-sm-scaled { border-radius: var(--radius-sm-scaled); }
.rounded-base-scaled { border-radius: var(--radius-base-scaled); }
.rounded-lg-scaled { border-radius: var(--radius-lg-scaled); }
.rounded-xl-scaled { border-radius: var(--radius-xl-scaled); }

/* 全局强制网格布局规则 - 覆盖所有媒体查询 */
.responsive-grid,
.info-cards-section,
.stats-cards,
.time-weather-section,
.data-grid,
.quick-overview,
.secondary-data-grid,
.bottom-data-grid,
.info-grid-redesigned,
.actions-grid {
  grid-template-columns: repeat(2, 1fr) !important;
  gap: var(--space-base-fluid) !important;
}

/* 特殊情况：某些网格可能需要不同的列数 */
.grid-1-col { grid-template-columns: 1fr !important; }
.grid-3-col { grid-template-columns: repeat(3, 1fr) !important; }
.grid-4-col { grid-template-columns: repeat(4, 1fr) !important; }

/* 确保所有文字和间距使用流体缩放 */
* {
  font-size: inherit;
}

/* 覆盖任何固定的rpx值 */
[class*="gap-"] {
  gap: var(--space-base-fluid) !important;
}

[class*="padding-"] {
  padding: var(--space-base-fluid) !important;
}

[class*="margin-"] {
  margin: var(--space-base-fluid) !important;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: linear-gradient(120deg, #1a1a2e 0%, #16213e 100%);
    --bg-secondary: #2a2a3e;
    --bg-card: #3a3a4e;
    --bg-overlay: rgba(58, 58, 78, 0.95);
    
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --text-light: #666666;
    
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.5);
  }
}
