<template>
  <!-- uni-app的App.vue不应该包含页面结构 -->
</template>

<script>
import { onMounted } from 'vue'
import mqttService from './utils/mqttService.js'
import themeManager from './utils/themeManager.js'
import { initResponsiveManager } from './utils/responsiveManager.js'
import { initDynamicScaleManager } from './utils/dynamicScaleManager.js'

export default {
  name: 'App',
  setup() {
    onMounted(async () => {
      // 初始化响应式管理器
      try {
        await initResponsiveManager()
        console.log('响应式管理器已初始化')
      } catch (error) {
        console.error('响应式管理器初始化失败:', error)
      }

      // 初始化动态缩放管理器
      try {
        await initDynamicScaleManager()
        console.log('动态缩放管理器已初始化')
      } catch (error) {
        console.error('动态缩放管理器初始化失败:', error)
      }

      // 初始化主题管理器
      try {
        themeManager.init()
        console.log('主题管理器已初始化')
      } catch (error) {
        console.error('主题管理器初始化失败:', error)
      }

      // 初始化MQTT连接
      try {
        mqttService.connect()
        mqttService.startSimulation()
        console.log('MQTT服务已启动')

        // 全局监听辐射警报消息，但不显示全局提示
        mqttService.onMessage('radiationAlert', (alertData) => {
          console.log('收到辐射警报（全局）:', alertData)
          // 不显示任何全局提示，只记录日志
          // 具体的提示由各个页面自行处理
        })

      } catch (error) {
        console.error('MQTT连接失败:', error)
        }
      })
  }
}
</script>

<style>
/* 全局样式 - 参考home.html设计语言 */
@import url('./uni.scss');
@import url('./styles/global.css');
@import url('./styles/responsive-utilities.css');
@import url('./styles/dynamic-scale.css');
@import url('./styles/layout-override.css');

/* 重置默认样式 */
* {
  box-sizing: border-box;
}

/* uni-app 全局样式 - 参考home.html */
page {
  background: var(--bg-primary);
  min-height: 100vh;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 去除默认margin和padding */
view, text, image, input, textarea, button {
  margin: 0;
  padding: 0;
}

/* 统一字体设置 */
text {
  font-family: inherit;
  word-break: break-all;
}

/* 输入框统一样式 */
input, textarea {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12rpx;
  color: #0f172a;
  font-size: 28rpx;
  font-weight: 500;
}

input:focus, textarea:focus {
  border-color: #00b4d8;
  outline: none;
  box-shadow: 0 0 0 3rpx rgba(0, 180, 216, 0.1);
  }

/* 按钮统一样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
}

.btn.primary {
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(0, 180, 216, 0.3);
}

.btn.primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 180, 216, 0.4);
}

.btn.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #0f172a;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(15, 23, 42, 0.06);
}

.btn.secondary:active {
  background: rgba(248, 250, 252, 0.9);
  transform: translateY(1rpx);
}

/* 卡片统一样式 */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(15, 23, 42, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(15, 23, 42, 0.10);
}

/* 加载状态统一样式 */
.loading-shimmer {
  background: linear-gradient(90deg, 
    rgba(248, 250, 252, 0.8) 25%, 
    rgba(255, 255, 255, 0.9) 50%, 
    rgba(248, 250, 252, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 悬停效果 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(15, 23, 42, 0.15);
}

/* 毛玻璃效果 */
.glass-effect {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 渐变背景类 */
.gradient-primary {
  background: linear-gradient(135deg, #00b4d8, #0096c7);
}

.gradient-secondary {
  background: linear-gradient(135deg, #10b981, #059669);
}

.gradient-accent {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .btn {
    padding: 20rpx 24rpx;
    font-size: 26rpx;
  }
  
  .card {
    border-radius: 20rpx;
}
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8rpx;
  height: 8rpx;
}

::-webkit-scrollbar-track {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 180, 216, 0.3);
  border-radius: 4rpx;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 180, 216, 0.5);
}

/* 底部导航栏优化 */
.uni-tabbar {
  background: rgba(254, 254, 254, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border-top: 1px solid rgba(226, 232, 240, 0.8) !important;
  box-shadow: 0 -4rpx 12rpx rgba(15, 23, 42, 0.08) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
}

.uni-tabbar .uni-tabbar__item {
  padding: 8rpx 0 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.uni-tabbar .uni-tabbar__bd {
  height: auto !important;
  padding-bottom: 4rpx !important;
}

.uni-tabbar .uni-tabbar__icon {
  width: 48rpx !important;
  height: 48rpx !important;
  margin-bottom: 4rpx !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.uni-tabbar .uni-tabbar__label {
  font-size: 20rpx !important;
  font-weight: 500 !important;
  color: #94a3b8 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 激活状态样式 */
.uni-tabbar .uni-tabbar__item.uni-tabbar__item--active .uni-tabbar__label {
  color: #00b4d8 !important;
  font-weight: 600 !important;
  transform: scale(1.05) !important;
}

.uni-tabbar .uni-tabbar__item.uni-tabbar__item--active .uni-tabbar__icon {
  transform: scale(1.1) !important;
  filter: drop-shadow(0 2rpx 8rpx rgba(0, 180, 216, 0.3)) !important;
}

/* 添加底部间距 */
page {
  padding-bottom: env(safe-area-inset-bottom, 0) !important;
}

.uni-app--showtabbar .uni-app__content {
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom, 0)) !important;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .uni-tabbar .uni-tabbar__icon {
    width: 44rpx !important;
    height: 44rpx !important;
  }
  
  .uni-tabbar .uni-tabbar__label {
    font-size: 18rpx !important;
  }
}
</style> 