/**
 * 响应式样式管理器
 * 统一管理应用的响应式样式和适配逻辑
 */

import { getDeviceInfo, pxToRpx, CommonSizes } from './adaptiveUtils.js';

class ResponsiveManager {
  constructor() {
    this.deviceInfo = null;
    this.isInitialized = false;
    this.listeners = [];
    this.currentBreakpoint = 'md'; // 默认中等屏幕
  }

  /**
   * 初始化响应式管理器
   */
  async init() {
    if (this.isInitialized) return;
    
    try {
      this.deviceInfo = await getDeviceInfo();
      this.currentBreakpoint = this.getBreakpoint();
      this.isInitialized = true;
      
      // 通知所有监听器
      this.notifyListeners();
      
      console.log('ResponsiveManager initialized:', {
        deviceInfo: this.deviceInfo,
        breakpoint: this.currentBreakpoint
      });
    } catch (error) {
      console.error('Failed to initialize ResponsiveManager:', error);
    }
  }

  /**
   * 获取当前设备的断点
   * 基于iPhone 14 Pro Max (430px) 为标准，使用比例缩放而非布局重排
   */
  getBreakpoint() {
    if (!this.deviceInfo) return 'md';

    const width = this.deviceInfo.windowWidth;

    // 重新设计断点，确保布局结构保持一致
    if (width < 280) return 'xs';      // 极小屏幕
    if (width < 320) return 'sm';      // 小屏幕
    if (width < 375) return 'md-';     // 中小屏幕
    if (width < 430) return 'md';      // 标准屏幕 (iPhone 14 Pro Max基准)
    if (width < 480) return 'md+';     // 中大屏幕
    if (width < 768) return 'lg';      // 大屏幕
    return 'xl';                       // 超大屏幕
  }

  /**
   * 添加响应式变化监听器
   */
  addListener(callback) {
    this.listeners.push(callback);
    
    // 如果已经初始化，立即调用回调
    if (this.isInitialized) {
      callback(this.deviceInfo, this.currentBreakpoint);
    }
  }

  /**
   * 移除监听器
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.deviceInfo, this.currentBreakpoint);
      } catch (error) {
        console.error('Error in responsive listener:', error);
      }
    });
  }

  /**
   * 获取响应式样式 - 使用比例缩放保持布局一致性
   */
  getResponsiveStyles(baseStyles) {
    if (!this.deviceInfo) return baseStyles;

    const styles = { ...baseStyles };
    const breakpoint = this.currentBreakpoint;

    // 根据断点调整样式，使用比例缩放而非布局重排
    switch (breakpoint) {
      case 'xs':
        return this.adjustStylesWithScale(styles, 0.75);  // 75%缩放
      case 'sm':
        return this.adjustStylesWithScale(styles, 0.85);  // 85%缩放
      case 'md-':
        return this.adjustStylesWithScale(styles, 0.92);  // 92%缩放
      case 'md':
        return styles; // 基准尺寸 (iPhone 14 Pro Max)
      case 'md+':
        return this.adjustStylesWithScale(styles, 1.05);  // 105%缩放
      case 'lg':
        return this.adjustStylesWithScale(styles, 1.1);   // 110%缩放
      case 'xl':
        return this.adjustStylesWithScale(styles, 1.15);  // 115%缩放
      default:
        return styles;
    }
  }

  /**
   * 统一的比例缩放样式调整方法
   * 保持布局结构不变，只调整尺寸比例
   */
  adjustStylesWithScale(styles, scale) {
    const scaledStyles = { ...styles };

    // 需要缩放的属性列表
    const scalableProps = [
      'fontSize', 'padding', 'margin', 'borderRadius', 'width', 'height',
      'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft',
      'marginTop', 'marginRight', 'marginBottom', 'marginLeft',
      'gap', 'rowGap', 'columnGap', 'lineHeight', 'letterSpacing'
    ];

    // 对所有可缩放属性进行比例调整
    scalableProps.forEach(prop => {
      if (scaledStyles[prop] !== undefined) {
        scaledStyles[prop] = this.scaleValue(scaledStyles[prop], scale);
      }
    });

    return scaledStyles;
  }

  /**
   * 缩放数值 - 支持多种单位类型
   */
  scaleValue(value, scale) {
    if (typeof value === 'number') {
      return Math.round(value * scale);
    }

    if (typeof value === 'string') {
      // 支持rpx、px、rem、em等单位
      const unitMatch = value.match(/^(\d+(?:\.\d+)?)(rpx|px|rem|em|%)$/);
      if (unitMatch) {
        const [, num, unit] = unitMatch;
        const scaledNum = Math.round(parseFloat(num) * scale);
        return `${scaledNum}${unit}`;
      }

      // 支持复合值，如 "10rpx 20rpx"
      if (value.includes('rpx') || value.includes('px')) {
        return value.replace(/(\d+(?:\.\d+)?)(rpx|px)/g, (match, num, unit) => {
          const scaledNum = Math.round(parseFloat(num) * scale);
          return `${scaledNum}${unit}`;
        });
      }
    }

    return value;
  }

  /**
   * 获取响应式字体大小
   */
  getResponsiveFontSize(baseFontSize) {
    const breakpoint = this.currentBreakpoint;
    const scales = {
      xs: 0.85,
      sm: 0.9,
      md: 1,
      lg: 1.1,
      xl: 1.15,
      xxl: 1.2
    };
    
    const scale = scales[breakpoint] || 1;
    return Math.round(baseFontSize * scale);
  }

  /**
   * 获取响应式间距
   */
  getResponsiveSpacing(baseSpacing) {
    const breakpoint = this.currentBreakpoint;
    const scales = {
      xs: 0.8,
      sm: 0.9,
      md: 1,
      lg: 1.1,
      xl: 1.15,
      xxl: 1.2
    };
    
    const scale = scales[breakpoint] || 1;
    return Math.round(baseSpacing * scale);
  }

  /**
   * 获取当前设备信息
   */
  getDeviceInfo() {
    return this.deviceInfo;
  }

  /**
   * 获取当前断点
   */
  getCurrentBreakpoint() {
    return this.currentBreakpoint;
  }

  /**
   * 判断是否为小屏设备
   */
  isSmallScreen() {
    return ['xs', 'sm', 'md-'].includes(this.currentBreakpoint);
  }

  /**
   * 判断是否为大屏设备
   */
  isLargeScreen() {
    return ['lg', 'xl'].includes(this.currentBreakpoint);
  }

  /**
   * 获取网格列数 - 保持布局结构一致性
   * 不再根据屏幕大小改变列数，而是通过缩放保持布局
   */
  getGridColumns(defaultColumns = 2) {
    // 始终返回默认列数，保持布局结构不变
    // 通过CSS缩放来适配不同屏幕，而不是改变网格结构
    return defaultColumns;
  }

  /**
   * 创建响应式CSS类名
   */
  createResponsiveClass(baseClass) {
    return `${baseClass} ${baseClass}--${this.currentBreakpoint}`;
  }
}

// 创建全局实例
const responsiveManager = new ResponsiveManager();

// 导出实例和工具函数
export default responsiveManager;

export {
  ResponsiveManager,
  responsiveManager,
  pxToRpx,
  CommonSizes
};

// 自动初始化（在应用启动时）
export const initResponsiveManager = async () => {
  await responsiveManager.init();
  return responsiveManager;
};
