/**
 * 动态缩放CSS系统
 * 基于iPhone 14 Pro Max (430px) 的动态缩放样式
 * 使用CSS变量和calc()函数实现精确的比例缩放
 */

/* ================== CSS变量定义 ================== */
:root {
  /* 动态缩放变量 */
  --dynamic-scale: 1;
  --dynamic-scale-inverse: 1;
  
  /* 基准尺寸（基于iPhone 14 Pro Max 430px） */
  --base-width: 430px;
  --base-height: 932px;
  
  /* 动态字体大小 */
  --font-xs-scaled: calc(24rpx * var(--dynamic-scale));
  --font-sm-scaled: calc(28rpx * var(--dynamic-scale));
  --font-base-scaled: calc(32rpx * var(--dynamic-scale));
  --font-lg-scaled: calc(36rpx * var(--dynamic-scale));
  --font-xl-scaled: calc(40rpx * var(--dynamic-scale));
  --font-2xl-scaled: calc(48rpx * var(--dynamic-scale));
  --font-3xl-scaled: calc(56rpx * var(--dynamic-scale));
  
  /* 动态间距 */
  --space-xs-scaled: calc(8rpx * var(--dynamic-scale));
  --space-sm-scaled: calc(12rpx * var(--dynamic-scale));
  --space-base-scaled: calc(16rpx * var(--dynamic-scale));
  --space-lg-scaled: calc(24rpx * var(--dynamic-scale));
  --space-xl-scaled: calc(32rpx * var(--dynamic-scale));
  --space-2xl-scaled: calc(48rpx * var(--dynamic-scale));
  --space-3xl-scaled: calc(64rpx * var(--dynamic-scale));
  
  /* 动态圆角 */
  --radius-xs-scaled: calc(4rpx * var(--dynamic-scale));
  --radius-sm-scaled: calc(8rpx * var(--dynamic-scale));
  --radius-base-scaled: calc(12rpx * var(--dynamic-scale));
  --radius-lg-scaled: calc(16rpx * var(--dynamic-scale));
  --radius-xl-scaled: calc(24rpx * var(--dynamic-scale));
  --radius-2xl-scaled: calc(32rpx * var(--dynamic-scale));
  
  /* 动态边框 */
  --border-width-scaled: calc(1rpx * var(--dynamic-scale));
  --border-width-2-scaled: calc(2rpx * var(--dynamic-scale));
  --border-width-4-scaled: calc(4rpx * var(--dynamic-scale));
}

/* ================== 动态缩放容器 ================== */
.dynamic-scale-container {
  width: 100%;
  height: 100%;
  transform-origin: top left;
  transform: scale(var(--dynamic-scale));
}

/* 反向缩放容器（用于需要保持原始大小的元素） */
.dynamic-scale-inverse {
  transform: scale(var(--dynamic-scale-inverse));
  transform-origin: center;
}

/* ================== 动态字体类 ================== */
.text-xs-scaled { font-size: var(--font-xs-scaled); }
.text-sm-scaled { font-size: var(--font-sm-scaled); }
.text-base-scaled { font-size: var(--font-base-scaled); }
.text-lg-scaled { font-size: var(--font-lg-scaled); }
.text-xl-scaled { font-size: var(--font-xl-scaled); }
.text-2xl-scaled { font-size: var(--font-2xl-scaled); }
.text-3xl-scaled { font-size: var(--font-3xl-scaled); }

/* ================== 动态间距类 ================== */
/* Padding */
.p-xs-scaled { padding: var(--space-xs-scaled); }
.p-sm-scaled { padding: var(--space-sm-scaled); }
.p-base-scaled { padding: var(--space-base-scaled); }
.p-lg-scaled { padding: var(--space-lg-scaled); }
.p-xl-scaled { padding: var(--space-xl-scaled); }
.p-2xl-scaled { padding: var(--space-2xl-scaled); }

/* Margin */
.m-xs-scaled { margin: var(--space-xs-scaled); }
.m-sm-scaled { margin: var(--space-sm-scaled); }
.m-base-scaled { margin: var(--space-base-scaled); }
.m-lg-scaled { margin: var(--space-lg-scaled); }
.m-xl-scaled { margin: var(--space-xl-scaled); }
.m-2xl-scaled { margin: var(--space-2xl-scaled); }

/* Gap */
.gap-xs-scaled { gap: var(--space-xs-scaled); }
.gap-sm-scaled { gap: var(--space-sm-scaled); }
.gap-base-scaled { gap: var(--space-base-scaled); }
.gap-lg-scaled { gap: var(--space-lg-scaled); }
.gap-xl-scaled { gap: var(--space-xl-scaled); }
.gap-2xl-scaled { gap: var(--space-2xl-scaled); }

/* ================== 动态圆角类 ================== */
.rounded-xs-scaled { border-radius: var(--radius-xs-scaled); }
.rounded-sm-scaled { border-radius: var(--radius-sm-scaled); }
.rounded-base-scaled { border-radius: var(--radius-base-scaled); }
.rounded-lg-scaled { border-radius: var(--radius-lg-scaled); }
.rounded-xl-scaled { border-radius: var(--radius-xl-scaled); }
.rounded-2xl-scaled { border-radius: var(--radius-2xl-scaled); }

/* ================== 动态边框类 ================== */
.border-scaled { border-width: var(--border-width-scaled); }
.border-2-scaled { border-width: var(--border-width-2-scaled); }
.border-4-scaled { border-width: var(--border-width-4-scaled); }

/* ================== 动态网格系统 ================== */
.grid-scaled {
  display: grid;
  gap: var(--space-base-scaled);
}

.grid-cols-1-scaled { grid-template-columns: 1fr; }
.grid-cols-2-scaled { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3-scaled { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4-scaled { grid-template-columns: repeat(4, 1fr); }

/* ================== 动态Flex系统 ================== */
.flex-scaled {
  display: flex;
  gap: var(--space-base-scaled);
}

.flex-col-scaled {
  display: flex;
  flex-direction: column;
  gap: var(--space-base-scaled);
}

/* ================== 动态卡片组件 ================== */
.card-scaled {
  padding: var(--space-lg-scaled);
  border-radius: var(--radius-lg-scaled);
  border: var(--border-width-scaled) solid var(--border-color, #e5e7eb);
  background: var(--bg-card, #ffffff);
  box-shadow: 0 calc(1rpx * var(--dynamic-scale)) calc(3rpx * var(--dynamic-scale)) rgba(0, 0, 0, 0.1);
}

.card-header-scaled {
  padding: var(--space-base-scaled);
  border-bottom: var(--border-width-scaled) solid var(--border-color, #e5e7eb);
  margin-bottom: var(--space-base-scaled);
}

.card-body-scaled {
  padding: var(--space-lg-scaled);
}

.card-footer-scaled {
  padding: var(--space-base-scaled);
  border-top: var(--border-width-scaled) solid var(--border-color, #e5e7eb);
  margin-top: var(--space-base-scaled);
}

/* ================== 动态按钮组件 ================== */
.btn-scaled {
  padding: var(--space-sm-scaled) var(--space-lg-scaled);
  border-radius: var(--radius-base-scaled);
  font-size: var(--font-base-scaled);
  border: var(--border-width-scaled) solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-sm-scaled {
  padding: var(--space-xs-scaled) var(--space-sm-scaled);
  font-size: var(--font-sm-scaled);
  border-radius: var(--radius-sm-scaled);
}

.btn-lg-scaled {
  padding: var(--space-base-scaled) var(--space-xl-scaled);
  font-size: var(--font-lg-scaled);
  border-radius: var(--radius-lg-scaled);
}

/* ================== 动态图标 ================== */
.icon-xs-scaled { 
  width: calc(16rpx * var(--dynamic-scale)); 
  height: calc(16rpx * var(--dynamic-scale)); 
}

.icon-sm-scaled { 
  width: calc(20rpx * var(--dynamic-scale)); 
  height: calc(20rpx * var(--dynamic-scale)); 
}

.icon-base-scaled { 
  width: calc(24rpx * var(--dynamic-scale)); 
  height: calc(24rpx * var(--dynamic-scale)); 
}

.icon-lg-scaled { 
  width: calc(32rpx * var(--dynamic-scale)); 
  height: calc(32rpx * var(--dynamic-scale)); 
}

.icon-xl-scaled { 
  width: calc(40rpx * var(--dynamic-scale)); 
  height: calc(40rpx * var(--dynamic-scale)); 
}

/* ================== 响应式工具类 ================== */
.w-full-scaled { width: 100%; }
.h-full-scaled { height: 100%; }

.min-h-screen-scaled { 
  min-height: calc(100vh * var(--dynamic-scale-inverse)); 
}

/* 保持宽高比 */
.aspect-square-scaled {
  aspect-ratio: 1 / 1;
}

.aspect-video-scaled {
  aspect-ratio: 16 / 9;
}

/* ================== 动画和过渡 ================== */
.transition-scaled {
  transition: all calc(0.2s * var(--dynamic-scale)) ease;
}

.transition-fast-scaled {
  transition: all calc(0.1s * var(--dynamic-scale)) ease;
}

.transition-slow-scaled {
  transition: all calc(0.3s * var(--dynamic-scale)) ease;
}
