/**
 * 精确比例缩放样式
 * 保持原有布局结构，只调整尺寸比例
 * 基于iPhone 14 Pro Max (430px) 为基准
 */

/* 全局比例缩放应用 */
* {
  /* 应用比例缩放到所有元素的基础属性 */
  --current-scale: var(--scale-factor-clamped);
}

/* 保持原有网格结构，只调整间距 */
.responsive-grid,
.info-cards-section,
.stats-cards,
.time-weather-section,
.data-grid,
.quick-overview,
.secondary-data-grid,
.bottom-data-grid,
.info-grid-redesigned,
.actions-grid,
.grid-2-cols,
.monitoring-grid,
.card-grid,
.feature-grid {
  gap: var(--space-base-scaled);
}

/* 确保卡片和容器使用比例缩放 */
.info-card,
.stat-card,
.data-item,
.card-item,
.grid-item {
  padding: var(--space-base-scaled);
  border-radius: var(--radius-base-scaled);
  font-size: var(--font-base-scaled);
  border-width: var(--border-width-scaled);
}

/* 标签文字使用较小字体 */
.info-label,
.stat-label,
.data-label,
.card-label {
  font-size: var(--font-sm-scaled);
}

/* 数值文字使用较大字体 */
.info-value,
.stat-value,
.data-value,
.card-value {
  font-size: var(--font-xl-scaled);
  font-weight: 600;
}

/* 大数值使用更大字体 */
.dose-rate-number,
.big-number,
.main-value {
  font-size: var(--font-3xl-scaled);
  font-weight: 700;
}

/* 单位文字 */
.dose-rate-unit,
.stat-unit,
.data-unit,
.value-unit {
  font-size: var(--font-xs-scaled);
}

/* 确保文字和数字保持在一行显示 */
.dose-rate-number,
.stat-value,
.data-value,
.metric-value,
.big-number,
.main-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  width: 100%;
}

/* 确保单位文字也在一行 */
.dose-rate-unit,
.stat-unit,
.data-unit,
.metric-unit,
.value-unit {
  white-space: nowrap;
}

/* 容器响应式调整 */
.container,
.index-container,
.dashboard-container {
  padding: clamp(12rpx, 2.5vw, 24rpx) !important;
  width: 100% !important;
  max-width: 100vw !important;
  box-sizing: border-box !important;
}

/* 确保所有图标大小适配 */
.icon,
.info-icon,
.data-icon,
.stat-icon {
  width: clamp(32rpx, 5vw, 48rpx) !important;
  height: clamp(32rpx, 5vw, 48rpx) !important;
}

/* 确保按钮大小适配 */
.btn,
.button,
.action-btn {
  padding: clamp(8rpx, 2vw, 16rpx) clamp(16rpx, 3vw, 24rpx) !important;
  font-size: clamp(24rpx, 3.5vw, 32rpx) !important;
  border-radius: clamp(8rpx, 2vw, 16rpx) !important;
}

/* 防止任何transform缩放影响布局 */
.responsive-container,
.scale-container {
  transform: none !important;
  width: 100% !important;
}
