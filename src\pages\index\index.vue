<template>
  <view class="index-container" :class="responsiveClasses">
    <!-- 现代化欢迎头部 -->
    <view class="modern-header-section">
      <view class="header-background">
        <view class="header-gradient"></view>
        <view class="header-pattern">
          <view class="pattern-dot dot-1"></view>
          <view class="pattern-dot dot-2"></view>
          <view class="pattern-dot dot-3"></view>
        </view>
      </view>
      <view class="header-content">
        <view class="welcome-section">
          <view class="app-logo-large">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          </view>
          <view class="welcome-text">
            <text class="welcome-title-modern">智能辐射监测</text>
            <text class="welcome-subtitle-modern">专业级实时监测 • 守护健康安全</text>
          </view>
        </view>

        <view class="user-section">
          <view class="user-avatar-modern">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </view>
          <view class="user-details">
            <text class="user-name-modern">用户</text>
            <view class="user-status-modern">
              <view class="status-indicator online"></view>
              <text class="status-text-modern">在线监测中</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 现代化信息卡片区域 -->
    <view class="info-cards-section">
      <view class="info-card time-card-modern">
        <view class="card-background">
          <view class="card-gradient time"></view>
          <view class="card-pattern"></view>
        </view>
        <view class="card-content">
          <view class="card-header">
            <view class="card-icon-wrapper time">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12,6 12,12 16,14"></polyline>
              </svg>
            </view>
            <text class="card-title">当前时间</text>
          </view>
          <view class="time-display">
            <text class="time-value">{{ currentTime }}</text>
            <text class="date-value">{{ currentDate }}</text>
          </view>
        </view>
      </view>

      <view class="info-card weather-card-modern">
        <view class="card-background">
          <view class="card-gradient weather"></view>
          <view class="card-pattern"></view>
        </view>
        <view class="card-content">
          <view class="card-header">
            <view class="card-icon-wrapper weather">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"></path>
              </svg>
            </view>
            <text class="card-title">天气信息</text>
          </view>
          <view class="weather-display">
            <view class="temperature-main">
              <text class="temp-value">{{ weather.temperature }}</text>
              <text class="temp-unit">°C</text>
            </view>
            <text class="weather-desc-modern">{{ weather.description }}</text>
            <view class="weather-details-modern">
              <view class="detail-item">
                <text class="detail-label">湿度</text>
                <text class="detail-value">{{ weather.humidity }}%</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">风速</text>
                <text class="detail-value">{{ weather.windSpeed }}km/h</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="info-card battery-card-modern">
        <view class="card-background">
          <view class="card-gradient battery"></view>
          <view class="card-pattern"></view>
        </view>
        <view class="card-content">
          <view class="card-header">
            <view class="card-icon-wrapper battery">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <rect x="1" y="6" width="18" height="12" rx="2" ry="2"></rect>
                <line x1="23" y1="13" x2="23" y2="11"></line>
              </svg>
            </view>
            <text class="card-title">电池状态</text>
          </view>
          <view class="battery-display">
            <text class="battery-value">{{ deviceStatus.battery }}%</text>
            <text class="battery-status">{{ deviceStatus.batteryStatus }}</text>
          </view>
        </view>
      </view>

      <view class="info-card signal-card-modern">
        <view class="card-background">
          <view class="card-gradient signal"></view>
          <view class="card-pattern"></view>
        </view>
        <view class="card-content">
          <view class="card-header">
            <view class="card-icon-wrapper signal">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M2 17h20v2H2zm1.15-4.05L4 11.47l.85 1.48L3.15 12.95zM6.31 8.7l.85 1.48L8 9.7l-.84-1.48L6.31 8.7zm3.16-3.16l.85 1.48L12 6.54l-.68-1.48-1.85.48zm6.84 0L15.46 7.02 16 8.5l1.69-1.48-.38-1.48z"></path>
              </svg>
            </view>
            <text class="card-title">信号强度</text>
          </view>
          <view class="signal-display">
            <text class="signal-value">{{ deviceStatus.signal }}</text>
            <text class="signal-status">{{ deviceStatus.networkType }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 现代化快速操作 -->
    <view class="quick-actions-modern">
      <view class="section-header-modern">
        <view class="section-icon-modern">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
          </svg>
        </view>
        <view class="section-text-modern">
          <text class="section-title-modern">快速访问</text>
          <text class="section-subtitle-modern">一键直达核心功能</text>
        </view>
      </view>

      <view class="actions-grid-modern">
        <view class="action-item-modern dashboard" @tap="navigateTo('/pages/dashboard/dashboard')">
          <view class="action-background">
            <view class="action-gradient dashboard"></view>
            <view class="action-pattern"></view>
          </view>
          <view class="action-content">
            <view class="action-icon-modern">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="9" y1="9" x2="9" y2="15"></line>
                <line x1="15" y1="9" x2="15" y2="15"></line>
              </svg>
            </view>
            <text class="action-text-modern">仪表盘</text>
            <text class="action-desc-modern">实时监控</text>
          </view>
        </view>

        <view class="action-item-modern health" @tap="navigateTo('/pages/health/health')">
          <view class="action-background">
            <view class="action-gradient health"></view>
            <view class="action-pattern"></view>
          </view>
          <view class="action-content">
            <view class="action-icon-modern">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"></path>
              </svg>
            </view>
            <text class="action-text-modern">健康监测</text>
            <text class="action-desc-modern">健康评估</text>
          </view>
        </view>

        <view class="action-item-modern charts" @tap="navigateTo('/pages/charts/charts')">
          <view class="action-background">
            <view class="action-gradient charts"></view>
            <view class="action-pattern"></view>
          </view>
          <view class="action-content">
            <view class="action-icon-modern">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="20" x2="18" y2="10"></line>
                <line x1="12" y1="20" x2="12" y2="4"></line>
                <line x1="6" y1="20" x2="6" y2="14"></line>
              </svg>
            </view>
            <text class="action-text-modern">数据分析</text>
            <text class="action-desc-modern">图表统计</text>
          </view>
        </view>

        <view class="action-item-modern settings" @tap="navigateTo('/pages/settings/settings')">
          <view class="action-background">
            <view class="action-gradient settings"></view>
            <view class="action-pattern"></view>
          </view>
          <view class="action-content">
            <view class="action-icon-modern">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
              </svg>
            </view>
            <text class="action-text-modern">设置</text>
            <text class="action-desc-modern">系统配置</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据概览 -->
    <view class="stats-overview">
      <view class="section-title">
        <text class="section-icon">📋</text>
        <text class="section-text">实时概览</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-header">
            <text class="stat-icon">☢️</text>
            <text class="stat-title">当前剂量率</text>
          </view>
          <text class="stat-value">{{ radiationState.currentData.doseRate.toFixed(2) }}</text>
          <text class="stat-unit">μSv/h</text>
          <view class="mini-chart">
            <canvas canvas-id="doseChart" class="chart-canvas"></canvas>
          </view>
        </view>
        
        <view class="stat-item">
          <view class="stat-header">
            <text class="stat-icon">📱</text>
            <text class="stat-title">设备状态</text>
          </view>
          <text class="stat-value">{{ deviceState.battery.level.toFixed(1) }}</text>
          <text class="stat-unit">% 电量</text>
          <view class="trend-indicator" :class="batteryTrend">
            <text class="trend-text">{{ batteryStatus }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近活动 -->
    <view class="recent-activity">
      <view class="section-title">
        <text class="section-icon">📝</text>
        <text class="section-text">最近活动</text>
      </view>
      <view class="activity-timeline">
        <view class="activity-item" v-for="(activity, index) in recentActivities" :key="index">
          <view class="activity-indicator" :class="activity.type"></view>
          <view class="activity-content">
            <text class="activity-title-text">{{ activity.title }}</text>
            <text class="activity-desc">{{ activity.description }}</text>
            <view class="activity-meta">
              <text class="activity-time">{{ formatTime(activity.timestamp) }}</text>
              <text class="activity-type">{{ activity.category }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 悬浮操作按钮 -->
    <view class="fab-container" :class="{ active: fabMenuOpen }" @tap="toggleFabMenu">
      <view class="fab-main">
        <text class="fab-icon">{{ fabMenuOpen ? '✕' : '📋' }}</text>
      </view>
      <view class="fab-menu">
        <view class="fab-item scan" @tap="quickScan">
          <text class="fab-item-icon">🔍</text>
        </view>
        <view class="fab-item alert" @tap="toggleAlert">
          <text class="fab-item-icon">🚨</text>
        </view>
        <view class="fab-item sync" @tap="syncData">
          <text class="fab-item-icon">🔄</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState, deviceState } from '../../utils/dataStore.js'
import responsiveMixin from '../../mixins/responsiveMixin.js'

export default {
  name: 'IndexPage',
  mixins: [responsiveMixin],
  setup() {
    const currentTime = ref('')
    const currentDate = ref('')
    const fabMenuOpen = ref(false)
    let timeInterval = null

    const weather = ref({
      temperature: 25,
      description: '晴朗',
      humidity: 65,
      windSpeed: 8
    })

    const recentActivities = ref([
      {
        title: '辐射检测正常',
        description: '设备运行良好，数据稳定',
        timestamp: Date.now() - 300000,
        type: 'success',
        category: '监测'
      },
      {
        title: '电池电量充足',
        description: '当前电量85%，续航良好',
        timestamp: Date.now() - 600000,
        type: 'info',
        category: '设备'
      },
      {
        title: '数据同步完成',
        description: '已上传到云端服务器',
        timestamp: Date.now() - 900000,
        type: 'success',
        category: '同步'
      }
    ])

    const batteryTrend = computed(() => {
      const level = deviceState.battery.level
      if (level > 50) return 'high'
      if (level > 20) return 'medium'
      return 'low'
    })

    const batteryStatus = computed(() => {
      const level = deviceState.battery.level
      if (deviceState.battery.charging) return '充电中'
      if (level > 50) return '电量充足'
      if (level > 20) return '电量正常'
      return '电量偏低'
    })

    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      currentDate.value = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }

    const navigateTo = (url) => {
      uni.navigateTo({ url })
    }

    const toggleFabMenu = () => {
      fabMenuOpen.value = !fabMenuOpen.value
    }

    const quickScan = () => {
      fabMenuOpen.value = false
      uni.showToast({
        title: '开始快速扫描',
        icon: 'success'
      })
    }

    const toggleAlert = () => {
      fabMenuOpen.value = false
      uni.showToast({
        title: '报警设置已切换',
        icon: 'success'
      })
    }

    const syncData = () => {
      fabMenuOpen.value = false
      uni.showLoading({
        title: '同步中...'
      })
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '数据同步成功',
          icon: 'success'
        })
      }, 2000)
    }

    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      const now = Date.now()
      const diff = now - timestamp
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return date.toLocaleDateString()
    }

    onMounted(() => {
      updateTime()
      timeInterval = setInterval(updateTime, 1000)
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      radiationState,
      deviceState,
      currentTime,
      currentDate,
      weather,
      recentActivities,
      batteryTrend,
      batteryStatus,
      fabMenuOpen,
      navigateTo,
      toggleFabMenu,
      quickScan,
      toggleAlert,
      syncData,
      formatTime
    }
  }
}
</script>

<style scoped>
.index-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  padding-bottom: var(--space-4xl);
  position: relative;
  overflow-x: hidden;
}

/* 响应式容器调整 */
.index-container.breakpoint-xs,
.index-container.breakpoint-sm {
  padding-bottom: var(--space-3xl);
}

.index-container.breakpoint-xl,
.index-container.breakpoint-xxl {
  padding-bottom: var(--space-4xl);
  max-width: 1200rpx;
  margin: 0 auto;
}

.index-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 20%, rgba(0, 180, 216, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 现代化头部区域 - 与地图页面保持一致的顶部间距 */
.modern-header-section {
  position: relative;
  z-index: 2;
  margin: 64px var(--space-lg) var(--space-xl);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
}

/* 小屏幕头部调整 - 与地图页面保持一致的顶部间距 */
.breakpoint-xs .modern-header-section,
.breakpoint-sm .modern-header-section {
  margin: 60px var(--space-base) var(--space-lg);
  border-radius: var(--radius-xl);
}

/* 大屏幕头部调整 */
.breakpoint-xl .modern-header-section,
.breakpoint-xxl .modern-header-section {
  margin: var(--space-2xl);
  margin-bottom: var(--space-3xl);
  border-radius: var(--radius-3xl);
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.header-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 50%,
    rgba(241, 245, 249, 0.85) 100%);
  backdrop-filter: blur(20px);
}

.header-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.pattern-dot {
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  opacity: 0.1;
}

.dot-1 {
  top: -60rpx;
  right: -60rpx;
  animation: float 6s ease-in-out infinite;
}

.dot-2 {
  bottom: -40rpx;
  left: -40rpx;
  animation: float 8s ease-in-out infinite reverse;
}

.dot-3 {
  top: 50%;
  right: 20rpx;
  width: 80rpx;
  height: 80rpx;
  animation: float 7s ease-in-out infinite;
}

.header-content {
  position: relative;
  z-index: 2;
  padding: var(--space-3xl) var(--space-lg);
}

/* 响应式头部内容调整 */
.breakpoint-xs .header-content,
.breakpoint-sm .header-content {
  padding: var(--space-2xl) var(--space-base);
}

.breakpoint-xl .header-content,
.breakpoint-xxl .header-content {
  padding: var(--space-4xl) var(--space-2xl);
}

.welcome-section {
  text-align: center;
  margin-bottom: var(--space-xl);
}

/* 响应式欢迎区域调整 */
.breakpoint-xs .welcome-section,
.breakpoint-sm .welcome-section {
  margin-bottom: var(--space-lg);
}

.breakpoint-xl .welcome-section,
.breakpoint-xxl .welcome-section {
  margin-bottom: var(--space-2xl);
}

.app-logo-large {
  width: var(--icon-2xl);
  height: var(--icon-2xl);
  margin: 0 auto var(--space-lg);
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  box-shadow: var(--shadow-lg);
  animation: float 3s ease-in-out infinite;
}

/* 响应式Logo调整 */
.breakpoint-xs .app-logo-large,
.breakpoint-sm .app-logo-large {
  width: var(--icon-xl);
  height: var(--icon-xl);
  margin-bottom: var(--space-base);
  border-radius: var(--radius-xl);
}

.breakpoint-xl .app-logo-large,
.breakpoint-xxl .app-logo-large {
  width: calc(var(--icon-2xl) * 1.2);
  height: calc(var(--icon-2xl) * 1.2);
  margin-bottom: var(--space-xl);
  border-radius: var(--radius-3xl);
}

.app-logo-large svg {
  width: 60rpx;
  height: 60rpx;
}

.welcome-text {
  text-align: center;
}

.welcome-title-modern {
  display: block;
  font-size: var(--font-3xl);
  color: #0f172a;
  font-weight: 800;
  margin-bottom: var(--space-sm);
  background: linear-gradient(135deg, #334155, #1e293b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

/* 响应式标题调整 */
.breakpoint-xs .welcome-title-modern,
.breakpoint-sm .welcome-title-modern {
  font-size: var(--font-2xl);
  margin-bottom: var(--space-xs);
}

.breakpoint-xl .welcome-title-modern,
.breakpoint-xxl .welcome-title-modern {
  font-size: var(--font-4xl);
  margin-bottom: var(--space-base);
}

.welcome-subtitle-modern {
  display: block;
  font-size: var(--font-lg);
  color: #64748b;
  font-weight: 500;
  line-height: 1.4;
}

/* 响应式副标题调整 */
.breakpoint-xs .welcome-subtitle-modern,
.breakpoint-sm .welcome-subtitle-modern {
  font-size: var(--font-base);
}

.breakpoint-xl .welcome-subtitle-modern,
.breakpoint-xxl .welcome-subtitle-modern {
  font-size: var(--font-xl);
}

.user-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.user-avatar-modern {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(139, 92, 246, 0.3);
}

.user-avatar-modern svg {
  width: 40rpx;
  height: 40rpx;
}

.user-details {
  flex: 1;
  margin-left: 20rpx;
}

.user-name-modern {
  display: block;
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.user-status-modern {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  position: relative;
}

.status-indicator.online {
  background: #10b981;
  box-shadow: 0 0 0 4rpx rgba(16, 185, 129, 0.2);
  animation: pulse 2s infinite;
}

.status-text-modern {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 600;
}

/* 现代化信息卡片区域 - 强制2x2网格布局 */
.info-cards-section {
  position: relative;
  z-index: 2;
  display: grid !important;
  grid-template-columns: 1fr 1fr !important; /* 强制2列 */
  grid-template-rows: auto auto !important; /* 强制2行 */
  gap: 24rpx !important;
  margin: 0 32rpx 40rpx !important;
  width: calc(100% - 64rpx) !important;
  box-sizing: border-box !important;
  /* 确保网格容器不会被其他样式覆盖 */
  flex-direction: unset !important;
  flex-wrap: unset !important;
}

/* 小屏幕调整 - 保持2x2但调整间距 */
@media (max-width: 375px) {
  .info-cards-section {
    gap: 16rpx !important;
    margin: 0 16rpx 32rpx !important;
    width: calc(100% - 32rpx) !important;
  }
}

/* 中等屏幕调整 */
@media (min-width: 376px) and (max-width: 414px) {
  .info-cards-section {
    gap: 20rpx !important;
    margin: 0 24rpx 36rpx !important;
    width: calc(100% - 48rpx) !important;
  }
}

/* 所有断点都保持2x2网格 */
.breakpoint-xs .info-cards-section,
.breakpoint-sm .info-cards-section,
.breakpoint-md .info-cards-section,
.breakpoint-lg .info-cards-section,
.breakpoint-xl .info-cards-section,
.breakpoint-xxl .info-cards-section {
  grid-template-columns: 1fr 1fr !important;
  grid-template-rows: auto auto !important;
  gap: 24rpx !important;
}

.info-card {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 200rpx;
  width: 100%;
  aspect-ratio: 1.2 / 1; /* 保持合适的宽高比 */
  display: flex;
  flex-direction: column;
}

/* 响应式信息卡片调整 */
.breakpoint-xs .info-card,
.breakpoint-sm .info-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
}

.breakpoint-xl .info-card,
.breakpoint-xxl .info-card {
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
}

.info-card:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 20rpx 60rpx rgba(15, 23, 42, 0.18);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.card-gradient.time {
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.9) 0%,
    rgba(124, 58, 237, 0.8) 100%);
}

.card-gradient.weather {
  background: linear-gradient(135deg,
    rgba(245, 158, 11, 0.9) 0%,
    rgba(217, 119, 6, 0.8) 100%);
}

.card-gradient.battery {
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.9) 0%,
    rgba(21, 128, 61, 0.8) 100%);
}

.card-gradient.signal {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.9) 0%,
    rgba(37, 99, 235, 0.8) 100%);
}

.card-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.card-content {
  position: relative;
  z-index: 2;
  padding: var(--space-lg);
  color: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 响应式卡片内容调整 */
.breakpoint-xs .card-content {
  padding: var(--space-base);
}

.breakpoint-sm .card-content {
  padding: var(--space-lg);
}

.breakpoint-xl .card-content,
.breakpoint-xxl .card-content {
  padding: var(--space-xl);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.card-icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.card-icon-wrapper svg {
  width: 24rpx;
  height: 24rpx;
}

.card-title {
  font-size: var(--font-sm);
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
}

/* 响应式卡片标题调整 */
.breakpoint-xs .card-title {
  font-size: var(--font-xs);
}

.breakpoint-xl .card-title,
.breakpoint-xxl .card-title {
  font-size: var(--font-base);
}

.time-display {
  text-align: center;
}

.time-value {
  display: block;
  font-size: var(--font-3xl);
  font-weight: 800;
  line-height: 1;
  margin-bottom: var(--space-xs);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 响应式时间数值调整 */
.breakpoint-xs .time-value {
  font-size: var(--font-2xl);
  margin-bottom: var(--space-xs);
}

.breakpoint-sm .time-value {
  font-size: var(--font-2xl);
}

.breakpoint-xl .time-value,
.breakpoint-xxl .time-value {
  font-size: var(--font-4xl);
}

.date-value {
  display: block;
  font-size: 22rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.weather-display {
  text-align: center;
}

.temperature-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 12rpx;
}

.temp-value {
  font-size: var(--font-3xl);
  font-weight: 800;
  line-height: 1;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 响应式温度数值调整 */
.breakpoint-xs .temp-value {
  font-size: var(--font-2xl);
}

.breakpoint-sm .temp-value {
  font-size: var(--font-2xl);
}

.breakpoint-xl .temp-value,
.breakpoint-xxl .temp-value {
  font-size: var(--font-4xl);
}

.temp-unit {
  font-size: 24rpx;
  font-weight: 600;
  margin-left: 4rpx;
  color: rgba(255, 255, 255, 0.8);
}

.weather-desc-modern {
  display: block;
  font-size: 22rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
}

.weather-details-modern {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.detail-item {
  flex: 1;
  text-align: center;
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  backdrop-filter: blur(10px);
}

.detail-label {
  display: block;
  font-size: 18rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4rpx;
}

.detail-value {
  display: block;
  font-size: 20rpx;
  font-weight: 700;
  color: #ffffff;
}

/* 现代化快速操作区域 */
.quick-actions-modern {
  position: relative;
  z-index: 2;
  margin: 0 32rpx 40rpx;
}

.section-header-modern {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
}

.section-icon-modern {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(0, 180, 216, 0.3);
}

.section-icon-modern svg {
  width: 24rpx;
  height: 24rpx;
}

.section-text-modern {
  flex: 1;
}

.section-title-modern {
  display: block;
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 4rpx;
}

.section-subtitle-modern {
  display: block;
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
}

.actions-grid-modern {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-base);
}

/* 响应式操作网格调整 */
.breakpoint-xs .actions-grid-modern {
  grid-template-columns: repeat(2, 1fr); /* 保持2列，但间距更小 */
  gap: var(--space-xs);
}

.breakpoint-sm .actions-grid-modern {
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-sm);
}

.breakpoint-xl .actions-grid-modern,
.breakpoint-xxl .actions-grid-modern {
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-lg);
}

.action-item-modern {
  position: relative;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(15, 23, 42, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.action-item-modern:hover {
  transform: translateY(-6rpx);
  box-shadow: 0 16rpx 48rpx rgba(15, 23, 42, 0.15);
}

.action-item-modern:active {
  transform: translateY(-3rpx) scale(0.98);
}

.action-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.action-gradient.dashboard {
  background: linear-gradient(135deg,
    rgba(0, 180, 216, 0.9) 0%,
    rgba(0, 150, 199, 0.8) 100%);
}

.action-gradient.health {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.9) 0%,
    rgba(220, 38, 38, 0.8) 100%);
}

.action-gradient.charts {
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.9) 0%,
    rgba(124, 58, 237, 0.8) 100%);
}

.action-gradient.settings {
  background: linear-gradient(135deg,
    rgba(100, 116, 139, 0.9) 0%,
    rgba(71, 85, 105, 0.8) 100%);
}

.action-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.action-content {
  position: relative;
  z-index: 2;
  padding: 32rpx 24rpx;
  text-align: center;
  color: #ffffff;
}

.action-icon-modern {
  width: 64rpx;
  height: 64rpx;
  margin: 0 auto 16rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon-modern svg {
  width: 32rpx;
  height: 32rpx;
}

.action-text-modern {
  display: block;
  font-size: 26rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.action-desc-modern {
  display: block;
  font-size: 20rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #00b4d8, #0096c7, #10b981, #8b5cf6);
  border-radius: 32rpx 32rpx 0 0;
}

.welcome-banner {
  text-align: center;
  margin-bottom: 32rpx;
}

.welcome-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  color: #00b4d8;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 180, 216, 0.2));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8rpx); }
}

.welcome-title {
  font-size: 42rpx;
  color: #0f172a;
  font-weight: 800;
  margin-bottom: 12rpx;
  background: linear-gradient(135deg, #334155, #1e293b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.5;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.avatar-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  box-shadow: 0 4rpx 12rpx rgba(0, 180, 216, 0.3);
}

.user-details {
  flex: 1;
  margin-left: 16rpx;
}

.user-name {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 6rpx;
}

.user-status {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
}

.device-status-mini {
  padding: 16rpx 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.device-status-mini:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(203, 213, 225, 0.8);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.08);
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  position: relative;
}

.status-dot.online {
  background: #10b981;
  box-shadow: 0 0 0 3rpx rgba(16, 185, 129, 0.2);
  animation: pulse 2s infinite;
}

.status-dot.offline {
  background: #94a3b8;
  box-shadow: 0 0 0 3rpx rgba(148, 163, 184, 0.2);
}

.status-text {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 600;
}

/* 时间天气区域 - 优雅设计 */
.time-weather-section {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.time-card, .weather-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
}

.time-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
  border-radius: 24rpx 24rpx 0 0;
}

.weather-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #f59e0b, #d97706);
  border-radius: 24rpx 24rpx 0 0;
}

.time-card:hover, .weather-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(15, 23, 42, 0.12);
  border-color: rgba(203, 213, 225, 0.9);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.card-icon {
  font-size: 28rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.card-title-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 700;
}

.current-time {
  font-size: 48rpx;
  color: #0f172a;
  font-weight: 800;
  margin-bottom: 8rpx;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(15, 23, 42, 0.08);
}

.current-date {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
}

.weather-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.weather-main {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.weather-icon {
  font-size: 56rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.weather-details {
  flex: 1;
}

.temperature {
  font-size: 36rpx;
  color: #0f172a;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 6rpx;
  text-shadow: 0 2rpx 4rpx rgba(15, 23, 42, 0.08);
}

.weather-desc {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.weather-extra {
  text-align: right;
}

.humidity, .wind {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
  line-height: 1.4;
}

/* 快速操作区域 - 现代设计 */
.quick-actions {
  position: relative;
  z-index: 2;
  margin-bottom: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.section-icon {
  font-size: 28rpx;
  color: #00b4d8;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 180, 216, 0.2));
}

.section-text {
  font-size: 30rpx;
  color: #0f172a;
  font-weight: 700;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 28rpx 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.action-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-item.dashboard::before {
  background: linear-gradient(90deg, #00b4d8, #0096c7);
}

.action-item.health::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.action-item.charts::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.action-item.settings::before {
  background: linear-gradient(90deg, #64748b, #475569);
}

.action-item:hover {
  transform: translateY(-6rpx);
  box-shadow: 0 16rpx 40rpx rgba(15, 23, 42, 0.15);
  border-color: rgba(203, 213, 225, 0.9);
}

.action-item:hover::before {
  height: 4rpx;
}

.action-item:active {
  transform: translateY(-3rpx) scale(0.98);
  box-shadow: 0 8rpx 25rpx rgba(15, 23, 42, 0.12);
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.action-text {
  font-size: 22rpx;
  color: #0f172a;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.action-desc {
  font-size: 18rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.3;
}

/* 统计卡片区域 - 优雅布局 */
.stats-overview {
  position: relative;
  z-index: 2;
  margin-bottom: 32rpx;
}

.stats-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  border-radius: 24rpx 24rpx 0 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card.radiation::before {
  background: linear-gradient(90deg, #06ffa5, #059669);
}

.stat-card.device::before {
  background: linear-gradient(90deg, #00b4d8, #0096c7);
}

.stat-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(15, 23, 42, 0.12);
  border-color: rgba(203, 213, 225, 0.9);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.stat-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.stat-icon {
  font-size: 28rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.stat-label {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 700;
}

.stat-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 600;
  border: 1px solid;
}

.stat-status.safe {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.2);
}

.stat-status.online {
  background: rgba(0, 180, 216, 0.1);
  color: #0077b6;
  border-color: rgba(0, 180, 216, 0.2);
}

.stat-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20rpx;
}

.stat-value {
  font-size: 44rpx;
  color: #0f172a;
  font-weight: 800;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(15, 23, 42, 0.08);
}

.stat-unit {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 600;
  margin-left: 6rpx;
}

.stat-chart {
  width: 100rpx;
  height: 60rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  overflow: hidden;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

.stat-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 600;
  border: 1px solid;
}

.stat-change.up {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.2);
}

.stat-change.down {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.2);
}

.stat-change.stable {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border-color: rgba(245, 158, 11, 0.2);
}

.change-icon {
  font-size: 16rpx;
}

.change-text {
  font-size: 18rpx;
  font-weight: 600;
}

.stat-time {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
}

/* 最近活动区域 - 现代时间线 */
.recent-activity {
  position: relative;
  z-index: 2;
}

.activity-list {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
}

.activity-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #8b5cf6, #7c3aed, #06ffa5);
  border-radius: 24rpx 24rpx 0 0;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.activity-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.activity-icon {
  font-size: 28rpx;
  color: #8b5cf6;
  filter: drop-shadow(0 2rpx 4rpx rgba(139, 92, 246, 0.2));
}

.activity-title-text {
  font-size: 26rpx;
  color: #0f172a;
  font-weight: 700;
}

.view-all-btn {
  padding: 12rpx 20rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 12rpx;
  color: #64748b;
  font-size: 20rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.view-all-btn:hover {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: #ffffff;
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.2);
}

.activity-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(203, 213, 225, 0.8);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.08);
}

.activity-indicator {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.activity-indicator.info {
  background: linear-gradient(135deg, #00b4d8, #0096c7);
}

.activity-indicator.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.activity-indicator.success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.activity-indicator.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.activity-content {
  flex: 1;
}

.activity-title-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.activity-desc {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-time {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
}

.activity-type {
  padding: 4rpx 12rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 10rpx;
  font-size: 16rpx;
  color: #64748b;
  font-weight: 600;
}

/* 底部导航增强 */
.bottom-nav-placeholder {
  height: 120rpx;
  margin-top: 32rpx;
}

/* 悬浮按钮 - 多功能设计 */
.fab-container {
  position: fixed;
  bottom: 140rpx;
  right: 40rpx;
  z-index: 100;
}

.fab-main {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 25rpx rgba(0, 180, 216, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.fab-main:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 15rpx rgba(0, 180, 216, 0.5);
}

.fab-icon {
  font-size: 40rpx;
  color: #ffffff;
}

.fab-menu {
  position: absolute;
  bottom: 120rpx;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.fab-container.active .fab-menu {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.fab-item {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.fab-item.scan {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.fab-item.alert {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.fab-item.sync {
  background: linear-gradient(135deg, #10b981, #059669);
}

.fab-item:active {
  transform: scale(0.9);
}

/* 响应式设计优化 */
@media (max-width: 750rpx) {
  .time-weather-section {
    grid-template-columns: 1fr 1fr !important; /* 保持2列 */
    gap: 16rpx;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }

  .stats-cards {
    grid-template-columns: 1fr 1fr !important; /* 保持2列 */
    gap: 16rpx;
  }

  .info-cards-section {
    grid-template-columns: 1fr 1fr !important; /* 强制保持2x2网格 */
    gap: 16rpx !important;
  }

  .index-container {
    padding: 16rpx;
  }
  
  .header-section {
    padding: 32rpx 24rpx;
  }
  
  .user-info {
    flex-direction: column;
    gap: 16rpx;
    text-align: center;
  }
  
  .current-time {
    font-size: 36rpx;
  }
  
  .temperature {
    font-size: 28rpx;
  }
  
  .stat-value {
  font-size: 36rpx;
  }
  
  .activity-item {
    flex-direction: column;
    gap: 12rpx;
    text-align: center;
  }
  
  .activity-meta {
    flex-direction: column;
    gap: 8rpx;
    align-items: center;
  }
}

/* 加载动画 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* 通知样式 */
.notification-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 24rpx;
  height: 24rpx;
  background: #ef4444;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  color: #ffffff;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}

/* 特殊效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
  background: linear-gradient(135deg, #00b4d8, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-soft {
  box-shadow: 0 4rpx 6rpx -1rpx rgba(0, 0, 0, 0.1), 0 2rpx 4rpx -1rpx rgba(0, 0, 0, 0.06);
}

.shadow-medium {
  box-shadow: 0 10rpx 15rpx -3rpx rgba(0, 0, 0, 0.1), 0 4rpx 6rpx -2rpx rgba(0, 0, 0, 0.05);
}

.shadow-large {
  box-shadow: 0 20rpx 25rpx -5rpx rgba(0, 0, 0, 0.1), 0 10rpx 10rpx -5rpx rgba(0, 0, 0, 0.04);
}

/* ================== 新增响应式断点系统 ================== */

/* 超小屏设备 (xs) 特殊调整 */
.breakpoint-xs .time-weather-section {
  grid-template-columns: 1fr 1fr !important; /* 保持2列布局 */
  gap: var(--space-sm) !important;
}

.breakpoint-xs .stats-cards {
  grid-template-columns: 1fr 1fr !important; /* 统计卡片保持2列 */
  gap: var(--space-sm) !important;
}

.breakpoint-xs .info-cards-section {
  grid-template-columns: 1fr 1fr !important; /* 信息卡片保持2x2网格 */
  gap: var(--space-sm) !important;
}

.breakpoint-xs .user-info {
  flex-direction: column !important;
  gap: var(--space-sm) !important;
  text-align: center !important;
}

.breakpoint-xs .activity-item {
  flex-direction: column !important;
  gap: var(--space-xs) !important;
  text-align: center !important;
}

.breakpoint-xs .activity-meta {
  flex-direction: column !important;
  gap: var(--space-xs) !important;
  align-items: center !important;
}

/* 小屏设备 (sm) 特殊调整 */
.breakpoint-sm .time-weather-section {
  grid-template-columns: 1fr 1fr !important; /* 小屏保持2列 */
  gap: var(--space-base) !important;
}

.breakpoint-sm .stats-cards {
  grid-template-columns: 1fr 1fr !important; /* 统计卡片保持2列 */
  gap: var(--space-base) !important;
}

.breakpoint-sm .user-info {
  flex-direction: column !important;
  gap: var(--space-base) !important;
  text-align: center !important;
}

/* 大屏设备 (xl, xxl) 特殊调整 */
.breakpoint-xl .time-weather-section,
.breakpoint-xxl .time-weather-section {
  grid-template-columns: 1fr 1fr !important;
  gap: var(--space-xl) !important;
}

.breakpoint-xl .stats-cards,
.breakpoint-xxl .stats-cards {
  grid-template-columns: repeat(3, 1fr) !important;
  gap: var(--space-xl) !important;
}

/* 平板设备特殊调整 */
.breakpoint-xxl .actions-grid-modern {
  grid-template-columns: repeat(4, 1fr) !important;
}

.breakpoint-xxl .info-cards-section {
  grid-template-columns: 1fr 1fr !important; /* 保持2x2网格，不改为3列 */
  gap: var(--space-xl) !important;
}

/* 设备类型特殊调整 */
.device-iphone .index-container {
  padding-bottom: calc(var(--space-4xl) + env(safe-area-inset-bottom)) !important;
}

.device-android .index-container {
  padding-bottom: var(--space-4xl) !important;
}

/* 像素密度调整 */
.pixel-ratio-3 .app-logo-large,
.pixel-ratio-4 .app-logo-large {
  box-shadow: var(--shadow-2xl) !important;
}
</style>
